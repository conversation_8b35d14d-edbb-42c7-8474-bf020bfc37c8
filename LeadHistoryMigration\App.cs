﻿using LeadHistoryMigration.Application.Interfaces;
using LeadHistoryMigration.Application.NewLeadHistory;
using LeadHistoryMigration.Infra.Database;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LeadHistoryMigration
{
    public class App
    {
        private readonly ILeadHistoryRepository _leadHistoryRepository;
        private readonly ILeadHistoryTransformer _leadHistoryTransformer;
        private readonly ApplicationDbContext _dbContext;

        public App(
            ILeadHistoryRepository leadHistoryRepository,
            ILeadHistoryTransformer leadHistoryTransformer,
            ApplicationDbContext dbContext)
        {
            _leadHistoryRepository = leadHistoryRepository;
            _leadHistoryTransformer = leadHistoryTransformer;
            _dbContext = dbContext;
        }

        public async Task RunAsync()
        {
            Console.WriteLine("Application started...");

            try
            {
                // Your business logic here
                // Example: Fetch data from repository
                var leadHistory = await _leadHistoryRepository.GetAllAsync();

                // Transform data
                var transformedData = await _leadHistoryTransformer.TransformMultipleToFieldWiseAsync(leadHistory);

                Console.WriteLine($"Processed {transformedData.Count()} records");

                Console.WriteLine("Application completed successfully.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                throw;
            }
        }
    }
}
