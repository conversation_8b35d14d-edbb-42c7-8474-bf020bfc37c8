﻿using LeadHistoryMigration.Application.Interfaces;
using LeadHistoryMigration.Application.NewLeadHistory;
using LeadHistoryMigration.Infra.Database;
using LeadHistoryMigration.Infra.NewLeadHistory;
using LeadHistoryMigration.Infra.Repos;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace LeadHistoryMigration.Infra
{
    public static class StartUp
    {
        public static IServiceCollection AddInfra(this IServiceCollection services, IConfiguration config)
        {
            services.AddDbContext<ApplicationDbContext>(options =>
            options.UseNpgsql(
                config.GetConnectionString("DefaultConnection"),
                b => b.MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName)
                )
            );

            services.AddTransient<ILeadHistoryRepository, LeadHistoryRepository>();
            services.AddTransient<ILeadHistoryTransformer, LeadHistoryTransformer>();

            return services;
        }
    }
}
