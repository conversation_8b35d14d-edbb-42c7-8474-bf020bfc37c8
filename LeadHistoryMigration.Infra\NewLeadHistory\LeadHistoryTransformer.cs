using LeadHistoryMigration.Application.ExistingHistory;
using LeadHistoryMigration.Application.NewLeadHistory;
using System.Collections.Concurrent;
using System.Globalization;
using System.Reflection;

namespace LeadHistoryMigration.Infra.NewLeadHistory
{
    /// <summary>
    /// Transforms lead history data from versioned dictionary format to field-wise change records.
    /// Optimized for performance and memory efficiency with comprehensive error handling.
    /// </summary>
    public class LeadHistoryTransformer : ILeadHistoryTransformer
    {
        private const string DefaultTenantId = "ratify";
        private const string DateTimeFormat = "MM/dd/yyyy HH:mm:ss";
        private const int ParallelProcessingThreshold = 10;

        // Cache for field metadata to avoid reflection overhead
        private static readonly ConcurrentDictionary<string, FieldMetadata> _fieldMetadataCache = new();

        // Configuration for transformation behavior
        private readonly TransformationOptions _options;

        /// <summary>
        /// Initializes a new instance of the LeadHistoryTransformer with default options.
        /// </summary>
        public LeadHistoryTransformer() : this(TransformationOptions.Default)
        {
        }

        /// <summary>
        /// Initializes a new instance of the LeadHistoryTransformer with custom options.
        /// </summary>
        /// <param name="options">Configuration options for transformation behavior</param>
        public LeadHistoryTransformer(TransformationOptions options)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));
        }

        /// <summary>
        /// Transforms a single lead history record to field-wise change records.
        /// </summary>
        /// <param name="leadHistory">The lead history record to transform</param>
        /// <returns>List of field-wise change records</returns>
        /// <exception cref="ArgumentNullException">Thrown when leadHistory is null</exception>
        public List<LeadHistoryHot> TransformToFieldWise(LeadHistory leadHistory)
        {
            if (leadHistory == null)
                throw new ArgumentNullException(nameof(leadHistory));

            var fieldWiseRecords = new List<LeadHistoryHot>();

            // Pre-cache common dictionaries to avoid repeated null checks
            var modifiedDates = leadHistory.ModifiedDate ?? new Dictionary<int, DateTime>();
            var lastModifiedByUsers = leadHistory.LastModifiedByUser ?? new Dictionary<int, Guid>();
            var assignedToUsers = leadHistory.AssignedToUser ?? new Dictionary<int, string>();

            // Generate a group key for this transformation batch
            var groupKey = Guid.NewGuid();

            // Process each JSONB field using optimized method
            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Name", typeof(string),
                leadHistory.Name, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Contact No", typeof(string),
                leadHistory.ContactNo, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Email", typeof(string),
                leadHistory.Email, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Alternate Contact No", typeof(string),
                leadHistory.AlternateContactNo, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Lead Number", typeof(string),
                leadHistory.LeadNumber, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Notes", typeof(string),
                leadHistory.Notes, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Confidential Notes", typeof(string),
                leadHistory.ConfidentialNotes, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Lower Budget", typeof(long),
                leadHistory.LowerBudget, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Upper Budget", typeof(long),
                leadHistory.UpperBudget, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Area", typeof(double),
                leadHistory.Area, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Area Unit", typeof(string),
                leadHistory.AreaUnit, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Currency", typeof(string),
                leadHistory.Currency, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Base Lead Status", typeof(string),
                leadHistory.BaseLeadStatus, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Sub Lead Status", typeof(string),
                leadHistory.SubLeadStatus, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Rating", typeof(string),
                leadHistory.Rating, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Scheduled Date", typeof(DateTime?),
                leadHistory.ScheduledDate, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Picked Date", typeof(DateTime?),
                leadHistory.PickedDate, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Is Picked", typeof(bool),
                leadHistory.IsPicked, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Assign To", typeof(Guid),
                leadHistory.AssignedTo, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Assigned User", typeof(string),
                leadHistory.AssignedToUser, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Assigned From User", typeof(string),
                leadHistory.AssignedFromUser, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Customer City", typeof(string),
                leadHistory.CustomerCity, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Customer State", typeof(string),
                leadHistory.CustomerState, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Customer Location", typeof(string),
                leadHistory.CustomerLocation, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Customer Country", typeof(string),
                leadHistory.CustomerCountry, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Enquired City", typeof(string),
                leadHistory.EnquiredCity, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Enquired State", typeof(string),
                leadHistory.EnquiredState, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Enquired Location", typeof(string),
                leadHistory.EnquiredLocation, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Enquired Country", typeof(string),
                leadHistory.EnquiredCountry, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Lead Source", typeof(LeadSource),
                leadHistory.LeadSource, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Enquired For", typeof(EnquiryType),
                leadHistory.EnquiredFor, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Sale Type", typeof(SaleType),
                leadHistory.SaleType, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Is Hot Lead", typeof(bool),
                leadHistory.IsHotLead, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Is Cold Lead", typeof(bool),
                leadHistory.IsColdLead, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            ProcessDictionaryFieldOptimized(fieldWiseRecords, leadHistory, "Is Warm Lead", typeof(bool),
                leadHistory.IsWarmLead, modifiedDates, lastModifiedByUsers, assignedToUsers, groupKey);

            return fieldWiseRecords;
        }

        /// <summary>
        /// Transforms multiple lead history records to field-wise change records asynchronously.
        /// Optimized for parallel processing and memory efficiency.
        /// </summary>
        /// <param name="leadHistories">The collection of lead history records to transform</param>
        /// <returns>Task containing the list of all field-wise change records</returns>
        /// <exception cref="ArgumentNullException">Thrown when leadHistories is null</exception>
        public async Task<List<LeadHistoryHot>> TransformMultipleToFieldWiseAsync(List<LeadHistory> leadHistories)
        {
            if (leadHistories == null)
                throw new ArgumentNullException(nameof(leadHistories));

            if (leadHistories.Count == 0)
                return new List<LeadHistoryHot>();

            // For small collections, process synchronously to avoid task overhead
            if (leadHistories.Count <= _options.ParallelProcessingThreshold)
            {
                var result = new List<LeadHistoryHot>();
                foreach (var leadHistory in leadHistories)
                {
                    try
                    {
                        result.AddRange(TransformToFieldWise(leadHistory));
                    }
                    catch (Exception ex) when (_options.ContinueOnError)
                    {
                        // Log error and continue processing if configured to do so
                        LogError($"Error processing lead history {leadHistory?.Id}: {ex.Message}", ex);
                    }
                }
                return result;
            }

            // For larger collections, use parallel processing
            var partitioner = Partitioner.Create(leadHistories, true);
            var allFieldWiseRecords = new ConcurrentBag<LeadHistoryHot>();

            await Task.Run(() =>
            {
                Parallel.ForEach(partitioner, leadHistory =>
                {
                    try
                    {
                        var records = TransformToFieldWise(leadHistory);
                        foreach (var record in records)
                        {
                            allFieldWiseRecords.Add(record);
                        }
                    }
                    catch (Exception ex) when (_options.ContinueOnError)
                    {
                        // Log the exception but continue processing other records
                        LogError($"Error processing lead history {leadHistory?.Id}: {ex.Message}", ex);
                    }
                });
            });

            return allFieldWiseRecords.ToList();
        }

        /// <summary>
        /// Optimized method to process dictionary fields and create field-wise change records.
        /// Uses efficient dictionary lookups and reduces object allocations.
        /// </summary>
        private void ProcessDictionaryFieldOptimized<T>(
            List<LeadHistoryHot> records,
            LeadHistory leadHistory,
            string fieldName,
            Type fieldType,
            IDictionary<int, T>? fieldData,
            IDictionary<int, DateTime> modifiedDates,
            IDictionary<int, Guid> lastModifiedByUsers,
            IDictionary<int, string> assignedToUsers,
            Guid groupKey)
        {
            if (fieldData == null || fieldData.Count == 0) return;

            // Get or create field metadata for type information
            var fieldMetadata = GetFieldMetadata(fieldName, fieldType);

            // Convert to array and sort once to avoid multiple enumerations
            var sortedVersions = fieldData.OrderBy(x => x.Key).ToArray();

            T? previousValue = default;
            bool hasPreviousValue = false;

            for (int i = 0; i < sortedVersions.Length; i++)
            {
                var (currentVersion, currentValue) = sortedVersions[i];

                // Convert values to strings efficiently
                string? oldValue = hasPreviousValue ? ConvertToStringOptimized(previousValue, fieldMetadata) : null;
                string? newValue = ConvertToStringOptimized(currentValue, fieldMetadata);

                // Only create record if there's a change
                if (!string.Equals(oldValue, newValue, StringComparison.Ordinal))
                {
                    // Use TryGetValue for efficient dictionary lookups
                    var modifiedDate = modifiedDates.TryGetValue(currentVersion, out var modDate)
                        ? modDate
                        : leadHistory.CreatedDate;

                    var lastModifiedBy = lastModifiedByUsers.TryGetValue(currentVersion, out var modById)
                        ? modById
                        : leadHistory.CreatedBy;

                    var modifiedByUserName = assignedToUsers.TryGetValue(currentVersion, out var userName)
                        ? userName
                        : string.Empty;

                    records.Add(new LeadHistoryHot
                    {
                        Id = Guid.NewGuid(),
                        LeadId = leadHistory.LeadId,
                        FieldName = fieldName,
                        FieldType = fieldMetadata.TypeName,
                        OldValue = oldValue,
                        NewValue = newValue,
                        ModifiedBy = modifiedByUserName,
                        ModifiedOn = modifiedDate,
                        LastModifiedById = lastModifiedBy,
                        Version = currentVersion,
                        GroupKey = groupKey,
                        UserId = leadHistory.UserId,
                        TenantId = _options.DefaultTenantId,
                        IsDeleted = leadHistory.IsDeleted
                    });
                }

                previousValue = currentValue;
                hasPreviousValue = true;
            }
        }

        /// <summary>
        /// Optimized string conversion method with caching and efficient type handling.
        /// </summary>
        private string? ConvertToStringOptimized<T>(T? value, FieldMetadata metadata)
        {
            if (value == null) return null;

            // Use cached type information for faster conversions
            return metadata.TypeCode switch
            {
                TypeCode.DateTime => ((DateTime)(object)value).ToString(_options.DateTimeFormat, CultureInfo.InvariantCulture),
                TypeCode.Boolean => (bool)(object)value ? "True" : "False",
                TypeCode.String => (string)(object)value,
                TypeCode.Int64 => ((long)(object)value).ToString(CultureInfo.InvariantCulture),
                TypeCode.Double => ((double)(object)value).ToString(CultureInfo.InvariantCulture),
                TypeCode.Object when value is Guid guid => guid.ToString(),
                TypeCode.Object when value is DateTime? nullableDateTime =>
                    nullableDateTime?.ToString(_options.DateTimeFormat, CultureInfo.InvariantCulture),
                TypeCode.Object when value is Enum => value.ToString(),
                _ => value.ToString()
            };
        }

        /// <summary>
        /// Gets or creates field metadata for efficient type operations.
        /// </summary>
        private static FieldMetadata GetFieldMetadata(string fieldName, Type fieldType)
        {
            return _fieldMetadataCache.GetOrAdd(fieldName, _ => new FieldMetadata(fieldName, fieldType));
        }

        /// <summary>
        /// Legacy method maintained for backward compatibility.
        /// Consider using ConvertToStringOptimized for better performance.
        /// </summary>
        [Obsolete("Use ConvertToStringOptimized for better performance")]
        private string? ConvertToString<T>(T? value)
        {
            if (value == null) return null;

            if (value is DateTime dateTime)
                return dateTime.ToString(DateTimeFormat, CultureInfo.InvariantCulture);

            if (value is bool boolValue)
                return boolValue ? "True" : "False";

            if (value is Guid guidValue)
                return guidValue.ToString();

            return value.ToString();
        }

        /// <summary>
        /// Logs errors during transformation. Override this method to integrate with your logging framework.
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="exception">Exception details</param>
        protected virtual void LogError(string message, Exception exception)
        {
            // Default implementation uses Debug output
            // In production, replace with your logging framework (e.g., ILogger, Serilog, NLog)
            System.Diagnostics.Debug.WriteLine($"[LeadHistoryTransformer] {message}");
            System.Diagnostics.Debug.WriteLine($"[LeadHistoryTransformer] Exception: {exception}");
        }
    }

    /// <summary>
    /// Metadata class for efficient field type operations and caching.
    /// </summary>
    internal sealed class FieldMetadata
    {
        public string FieldName { get; }
        public Type FieldType { get; }
        public string TypeName { get; }
        public TypeCode TypeCode { get; }

        public FieldMetadata(string fieldName, Type fieldType)
        {
            FieldName = fieldName ?? throw new ArgumentNullException(nameof(fieldName));
            FieldType = fieldType ?? throw new ArgumentNullException(nameof(fieldType));

            // Handle nullable types
            var underlyingType = Nullable.GetUnderlyingType(fieldType) ?? fieldType;
            TypeCode = Type.GetTypeCode(underlyingType);

            // Generate clean type name
            TypeName = GenerateTypeName(fieldType);
        }

        private static string GenerateTypeName(Type type)
        {
            if (type == typeof(string)) return "String";
            if (type == typeof(bool)) return "Boolean";
            if (type == typeof(long)) return "Int64";
            if (type == typeof(double)) return "Double";
            if (type == typeof(Guid)) return "Guid";
            if (type == typeof(DateTime?)) return "DateTime?";
            if (type == typeof(DateTime)) return "DateTime";
            if (type.IsEnum) return type.Name;

            return type.Name;
        }
    }

    /// <summary>
    /// Configuration options for the LeadHistoryTransformer.
    /// </summary>
    public sealed class TransformationOptions
    {
        /// <summary>
        /// Default configuration options.
        /// </summary>
        public static readonly TransformationOptions Default = new();

        /// <summary>
        /// Threshold for switching from sequential to parallel processing.
        /// Default is 10 records.
        /// </summary>
        public int ParallelProcessingThreshold { get; set; } = 10;

        /// <summary>
        /// Whether to continue processing other records when an error occurs.
        /// Default is true.
        /// </summary>
        public bool ContinueOnError { get; set; } = true;

        /// <summary>
        /// Default tenant ID to use when not specified.
        /// Default is "ratify".
        /// </summary>
        public string DefaultTenantId { get; set; } = "ratify";

        /// <summary>
        /// Date time format for string conversion.
        /// Default is "MM/dd/yyyy HH:mm:ss".
        /// </summary>
        public string DateTimeFormat { get; set; } = "MM/dd/yyyy HH:mm:ss";

        /// <summary>
        /// Maximum degree of parallelism for parallel processing.
        /// Default is -1 (use all available processors).
        /// </summary>
        public int MaxDegreeOfParallelism { get; set; } = -1;

        /// <summary>
        /// Whether to validate input parameters strictly.
        /// Default is true.
        /// </summary>
        public bool StrictValidation { get; set; } = true;
    }
}
