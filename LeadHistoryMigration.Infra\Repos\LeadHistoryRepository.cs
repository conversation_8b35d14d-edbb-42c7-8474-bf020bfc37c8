﻿using LeadHistoryMigration.Application.ExistingHistory;
using LeadHistoryMigration.Application.Interfaces;
using LeadHistoryMigration.Infra.Database;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace LeadHistoryMigration.Infra.Repos
{
    public class LeadHistoryRepository : ILeadHistoryRepository
    {
        private readonly ApplicationDbContext _context;

        public LeadHistoryRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<LeadHistory?> GetByIdAsync(Guid id, bool includeDeleted = false)
        {
            var query = _context.LeadHistories.AsNoTracking();

            if (!includeDeleted)
                query = query.Where(x => !x.IsDeleted);

            return await query.FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task<List<LeadHistory>> GetByLeadIdAsync(Guid leadId, bool includeDeleted = false)
        {
            var query = _context.LeadHistories
                .AsNoTracking()
                .Where(x => x.LeadId == leadId);

            if (!includeDeleted)
                query = query.Where(x => !x.IsDeleted);

            return await query
                .OrderByDescending(x => x.CurrentVersion)
                .ToListAsync();
        }

        public async Task<LeadHistory?> GetLatestVersionByLeadIdAsync(Guid leadId)
        {
            return await _context.LeadHistories
                .AsNoTracking()
                .Where(x => x.LeadId == leadId && !x.IsDeleted)
                .OrderByDescending(x => x.CurrentVersion)
                .FirstOrDefaultAsync();
        }

        public async Task<List<LeadHistory>> GetAllAsync(int pageNumber = 1, int pageSize = 100, bool includeDeleted = false)
        {
            var query = _context.LeadHistories.AsNoTracking();

            if (!includeDeleted)
                query = query.Where(x => !x.IsDeleted);

            return await query
                .OrderByDescending(x => x.CreatedDate)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<List<LeadHistory>> GetByUserIdAsync(Guid userId, bool includeDeleted = false)
        {
            var query = _context.LeadHistories
                .AsNoTracking()
                .Where(x => x.UserId == userId);

            if (!includeDeleted)
                query = query.Where(x => !x.IsDeleted);

            return await query
                .OrderByDescending(x => x.CreatedDate)
                .ToListAsync();
        }

        public async Task<List<LeadHistory>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, bool includeDeleted = false)
        {
            var query = _context.LeadHistories
                .AsNoTracking()
                .Where(x => x.CreatedDate >= startDate && x.CreatedDate <= endDate);

            if (!includeDeleted)
                query = query.Where(x => !x.IsDeleted);

            return await query
                .OrderByDescending(x => x.CreatedDate)
                .ToListAsync();
        }

        public async Task<int> GetCountAsync(bool includeDeleted = false)
        {
            var query = _context.LeadHistories.AsQueryable();

            if (!includeDeleted)
                query = query.Where(x => !x.IsDeleted);

            return await query.CountAsync();
        }

        public async Task<List<LeadHistory>> FindAsync(Expression<Func<LeadHistory, bool>> predicate, int pageNumber = 1, int pageSize = 100)
        {
            return await _context.LeadHistories
                .AsNoTracking()
                .Where(predicate)
                .OrderByDescending(x => x.CreatedDate)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }
    }
}
